import { DataTable } from "primereact/datatable";
import CourseCard from "../../components/LMS/CourseCard";
import ProfileBanner from "../../components/LMS/ProfileBanner";
import { PageContainer } from "../../components/UI/Page/PageContainer/PageContainer";
import { Column } from "primereact/column";
import styles from "./index.module.css";
import clsx from "clsx";
import { InputText } from "primereact/inputtext";
import PdfThumbnail from "../../svg/metronic/LMS_pdf.svg";
import Image from "next/image";
import EmptyDataState from "../../svg/metronic/LMS_empty_data_state.svg";
import Button from "../../components/UI/Button/Button";
import { ConditionalDisplay } from "../../components/UI/ConditionalDisplay/ConditionalDisplay";
import { useApi } from "../../hooks/useApi";
import { useDashboard } from "../../hooks/useDashboard";
import { useContext, useEffect } from "react";
import UserProfileContext from "../../public/UserProfileContext/UserProfileContext";
import VideoPlayer from "../../components/Video/VideoPlayer";

export default function LMSDashboard() {
  const { callApi, loading } = useApi();
  const { userID: userId } = useContext(UserProfileContext);

  const {
    rows,
    setRows,
    totalCount,
    setTotalCount,
    lazyParams,
    globalFilter,
    onSort,
    onPage,
    onGlobalFilterChange,
    lazyParamsToQueryString,
  } = useDashboard({
    initialLazyParams: {
      first: 0,
      rows: 10,
      sortOrder: -1,
      page: 0,
      filters: {
        global: { value: "", matchMode: "contains" },
      },
    },
  });

  useEffect(() => {
    const fetchCourses = async () => {
      const queryString = lazyParamsToQueryString(lazyParams); // Convert lazyParams to query string
      try {
        const response = await callApi({
          method: "GET",
          url: `Leads/GetAllLeadCourseMapping${queryString}&LeadUserProfileId=${userId}`,
        });
        if (response?.data) {
          setRows(response.data.rows);
          setTotalCount(response.data.count);
        }
      } catch (error) {
        console.error("Error fetching form definition options:", error);
      }
    };

    if (userId) {
      fetchCourses();
    }
  }, [lazyParams, userId]);

  const tableheader = (headerText) => (
    <div className={styles.prospecTable}>
      <span className="text-xl font-bold">{headerText}</span>
      {/* <span
        className="p-input-icon-left"
        style={{
          display: "flex",
          flexDirection: "row-reverse",
        }}
      >
        <i
          className={clsx("pi pi-search", styles.searchIcon)}
          style={{ top: "22px" }}
        />
        <InputText placeholder="Search" className={styles.search} />
      </span> */}
    </div>
  );

  const courseTypeTemplate = () => (
    <Image src={PdfThumbnail} alt="Course Card Background"></Image>
  );

  const emptyDataState = () => (
    <div className="flex flex-column align-items-center">
      <Image src={EmptyDataState} alt="Empty Data State" />
      <span className="text-center">
        You have no pending quizzes at the moment
      </span>
    </div>
  );

  const classDescriptionTemplate = (showDescription = false) => (
    <div>
      <div
        style={{
          fontFamily: "Open Sans",
          fontWeight: 700,
          fontSize: "16px",
          lineHeight: "100%",
          letterSpacing: "0%",
          color: "rgba(81, 81, 81, 1)",
        }}
      >
        {"Build a Free Website with WordPress"}
      </div>
      <ConditionalDisplay condition={showDescription}>
        <div
          style={{
            fontFamily: "Open Sans",
            fontWeight: 400,
            fontSize: "14px",
            lineHeight: "100%",
            letterSpacing: "0%",
            color: "rgba(81, 81, 81, 1)",
            paddingTop: "0.25rem",
          }}
        >
          {"Chap 2 - English for Career Development"}
        </div>
      </ConditionalDisplay>

      <div
        style={{
          fontFamily: "Open Sans",
          fontWeight: 400,
          fontStyle: "italic",
          fontSize: "14px",
          lineHeight: "100%",
          letterSpacing: "0%",
          paddingTop: "0.25rem",
          color: "rgba(81, 81, 81, 1)",
        }}
      >
        {"by Steve H"}
      </div>
    </div>
  );

  const actionTemplate = (actionLabel) => (
    <Button
      variant="outline"
      label={actionLabel}
      onClick={() => {}}
      width={"13rem"}
      theme="metronic"
    ></Button>
  );

  const courseColumns = [
    { field: "icon", header: "", body: courseTypeTemplate },
    { field: "desc", header: "", body: () => classDescriptionTemplate(true) },
    { field: "resume", header: "", body: () => actionTemplate("Resume") },
  ];

  const certificateColumns = [
    { field: "icon", header: "", body: courseTypeTemplate },
    { field: "desc", header: "", body: () => classDescriptionTemplate(false) },
    {
      field: "resume",
      header: "",
      body: () => actionTemplate("View Certificate"),
    },
  ];

  const mockRows = [
    {
      icon: "icon",
      desc: "desc",
      resume: "resume",
    },
    {
      icon: "icon",
      desc: "desc",
      resume: "resume",
    },
    {
      icon: "icon",
      desc: "desc",
      resume: "resume",
    },
  ];

  return (
    <PageContainer theme="metronic">
      <ProfileBanner />
      <div
        style={{
          fontFamily: "Open Sans",
          fontWeight: 800,
          fontSize: "16px",
          lineHeight: "100%",
          letterSpacing: "0%",
          textTransform: "uppercase",
          color: "rgba(81, 81, 81, 1)",
          marginTop: "1.25rem",
          marginBottom: "1.25rem",
        }}
      >
        {"Available Courses"}
      </div>
      <div className="flex flex-wrap gap-4 justify-content-between">
        <CourseCard
          courseTitle={"Course Title"}
          courseDescription={"Course Description"}
          assignedDate={"Assigned Date"}
        />
        <CourseCard
          courseTitle={"Course Title"}
          courseDescription={"Course Description"}
          assignedDate={"Assigned Date"}
        />
        <CourseCard
          courseTitle={"Course Title"}
          courseDescription={"Course Description"}
          assignedDate={"Assigned Date"}
        />
        <CourseCard
          courseTitle={"Course Title"}
          courseDescription={"Course Description"}
          assignedDate={"Assigned Date"}
        />
      </div>
      <section style={{ marginBottom: "40px" }}>
        <h2>YouTube Video</h2>
        <VideoPlayer
          src="https://www.youtube.com/watch?v=xNMl_CdQ4QA&list=PLfZcU0E-cRgAbjixpnXDVfcC68WwLQoQB"
          width="100%"
          height="400px"
          autoplay={true}
          muted={true}
          onLoad={() => console.log("YouTube video loaded")}
          onError={(error) => console.error("YouTube video error:", error)}
        />
      </section>
      <div
        style={{
          marginTop: "1.25rem",
          marginBottom: "1.25rem",
        }}
      >
        <DataTable
          header={() => tableheader("Ongoing Classes")}
          className="custom-lead"
          value={mockRows}
          paginator
          rows={10}
          style={{ cursor: "pointer" }}
          showHeaders={false}
        >
          {courseColumns.map((col, index) => (
            <Column
              key={index}
              field={col.field}
              header={col.header}
              style={
                index === 0
                  ? { width: "6%", textAlign: "center" }
                  : index === courseColumns.length - 1
                  ? { width: "16%", textAlign: "center" }
                  : {}
              }
              {...(col.body ? { body: col.body } : {})}
            />
          ))}
        </DataTable>
      </div>
      <div
        style={{
          marginTop: "1.25rem",
          marginBottom: "1.25rem",
        }}
      >
        <DataTable
          header={() => tableheader("Pending Quizzes")}
          className="custom-lead"
          value={[]}
          paginator
          rows={10}
          style={{ cursor: "pointer" }}
          emptyMessage={emptyDataState}
          showHeaders={false}
        >
          {courseColumns.map((col, index) => (
            <Column
              key={index}
              field={col.field}
              header={col.header}
              {...(col.body ? { body: col.body } : {})}
            />
          ))}
        </DataTable>
      </div>
      <div
        style={{
          marginTop: "1.25rem",
          marginBottom: "1.25rem",
        }}
      >
        <DataTable
          header={() => tableheader("Your Certification")}
          className="custom-lead"
          value={mockRows}
          paginator
          rows={10}
          style={{ cursor: "pointer" }}
          showHeaders={false}
        >
          {certificateColumns.map((col, index) => (
            <Column
              key={index}
              field={col.field}
              header={col.header}
              style={
                index === 0
                  ? { width: "6%", textAlign: "center" }
                  : index === certificateColumns.length - 1
                  ? { width: "16%", textAlign: "center" }
                  : {}
              }
              {...(col.body ? { body: col.body } : {})}
            />
          ))}
        </DataTable>
      </div>
    </PageContainer>
  );
}
