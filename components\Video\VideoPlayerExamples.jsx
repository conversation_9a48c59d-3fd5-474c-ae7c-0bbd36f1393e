import VideoPlayer from "./VideoPlayer";

/**
 * Examples demonstrating how to use the enhanced VideoPlayer component
 * with different video sources and configurations
 */

const VideoPlayerExamples = () => {
  // Example Azure Storage configuration
  const azureConfig = {
    accountName: "yourstorageaccount",
    containerName: "videos",
    sasToken: "sv=2022-11-02&ss=bfqt&srt=sco&sp=rwdlacupiytfx&se=2024-12-31T23:59:59Z&st=2024-01-01T00:00:00Z&spr=https&sig=YourSASTokenHere"
  };

  return (
    <div style={{ padding: "20px", maxWidth: "1200px", margin: "0 auto" }}>
      <h1>Enhanced VideoPlayer Examples</h1>
      
      {/* YouTube Video Example */}
      <section style={{ marginBottom: "40px" }}>
        <h2>YouTube Video</h2>
        <VideoPlayer
          src="https://www.youtube.com/watch?v=dQw4w9WgXcQ"
          width="100%"
          height="400px"
          autoplay={true}
          muted={true}
          onLoad={() => console.log("YouTube video loaded")}
          onError={(error) => console.error("YouTube video error:", error)}
        />
      </section>

      {/* YouTube Short URL Example */}
      <section style={{ marginBottom: "40px" }}>
        <h2>YouTube Short URL</h2>
        <VideoPlayer
          src="https://youtu.be/dQw4w9WgXcQ"
          width="100%"
          height="400px"
        />
      </section>

      {/* LinkedIn Video Example */}
      <section style={{ marginBottom: "40px" }}>
        <h2>LinkedIn Video</h2>
        <VideoPlayer
          src="https://www.linkedin.com/posts/example-post-id"
          width="100%"
          height="400px"
          autoplay={false}
        />
      </section>

      {/* Azure Media Storage Example */}
      <section style={{ marginBottom: "40px" }}>
        <h2>Azure Media Storage Video</h2>
        <VideoPlayer
          src="https://yourstorageaccount.streaming.media.azure.net/video.mp4"
          azureStorageConfig={azureConfig}
          width="100%"
          height="400px"
          autoplay={true}
          muted={true}
          poster="https://yourstorageaccount.blob.core.windows.net/thumbnails/video-poster.jpg"
        />
      </section>

      {/* Direct Video File with Multiple Sources */}
      <section style={{ marginBottom: "40px" }}>
        <h2>Direct Video with Alternative Sources</h2>
        <VideoPlayer
          src="https://example.com/video.mp4"
          alternativeSources={[
            "https://backup1.example.com/video.mp4",
            "https://backup2.example.com/video.webm",
            "https://backup3.example.com/video.ogg"
          ]}
          type="video/mp4"
          width="100%"
          height="400px"
          autoplay={true}
          muted={true}
          controls={true}
          poster="https://example.com/poster.jpg"
          retryAttempts={5}
          fallbackText="Unable to load video. Please check your internet connection."
        />
      </section>

      {/* Custom Styled Video Player */}
      <section style={{ marginBottom: "40px" }}>
        <h2>Custom Styled Video Player</h2>
        <VideoPlayer
          src="https://example.com/video.mp4"
          width="800px"
          height="450px"
          className="custom-video-player"
          style={{
            border: "2px solid #007bff",
            borderRadius: "12px",
            boxShadow: "0 4px 8px rgba(0,0,0,0.1)"
          }}
          autoplay={false}
          controls={true}
          showLoadingSpinner={true}
        />
      </section>

      {/* Video with Error Handling */}
      <section style={{ marginBottom: "40px" }}>
        <h2>Video with Custom Error Handling</h2>
        <VideoPlayer
          src="https://invalid-url.com/nonexistent-video.mp4"
          width="100%"
          height="400px"
          onError={(error) => {
            console.error("Custom error handler:", error);
            // You can implement custom error tracking here
          }}
          fallbackText="This video is currently unavailable. Please try again later."
          retryAttempts={2}
        />
      </section>

      {/* Responsive Video Player */}
      <section style={{ marginBottom: "40px" }}>
        <h2>Responsive Video Player</h2>
        <div style={{ 
          position: "relative", 
          paddingBottom: "56.25%", // 16:9 aspect ratio
          height: 0,
          overflow: "hidden"
        }}>
          <VideoPlayer
            src="https://example.com/video.mp4"
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%"
            }}
            autoplay={true}
            muted={true}
          />
        </div>
      </section>

      {/* Azure Blob Storage with SAS Token */}
      <section style={{ marginBottom: "40px" }}>
        <h2>Azure Blob Storage with SAS Token</h2>
        <VideoPlayer
          src="https://yourstorageaccount.blob.core.windows.net/videos/sample-video.mp4"
          azureStorageConfig={{
            accountName: "yourstorageaccount",
            sasToken: "sv=2022-11-02&ss=b&srt=o&sp=r&se=2024-12-31T23:59:59Z&st=2024-01-01T00:00:00Z&spr=https&sig=YourSASTokenHere"
          }}
          width="100%"
          height="400px"
          autoplay={true}
          muted={true}
        />
      </section>

      {/* Video with No Loading Spinner */}
      <section style={{ marginBottom: "40px" }}>
        <h2>Video without Loading Spinner</h2>
        <VideoPlayer
          src="https://example.com/video.mp4"
          width="100%"
          height="400px"
          showLoadingSpinner={false}
          autoplay={true}
          muted={true}
        />
      </section>

      <style jsx>{`
        .custom-video-player {
          transition: transform 0.3s ease;
        }
        .custom-video-player:hover {
          transform: scale(1.02);
        }
      `}</style>
    </div>
  );
};

export default VideoPlayerExamples;
