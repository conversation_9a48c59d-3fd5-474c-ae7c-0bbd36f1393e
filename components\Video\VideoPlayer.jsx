import { ConditionalDisplay } from "../UI/ConditionalDisplay/ConditionalDisplay";

const VideoPlayer = ({ src, type = "video/mp4", embed = false }) => {
  return (
    <>
      <ConditionalDisplay condition={embed}>
        <iframe
          width="560"
          height="315"
          src={src}
          title="YouTube video player"
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
        ></iframe>
      </ConditionalDisplay>
      <ConditionalDisplay condition={!embed}>
        <video width="320" height="240" controls preload="none">
          <source src={src} type={type} />
          Your browser does not support the video tag.
        </video>
      </ConditionalDisplay>
    </>
  );
};

export default VideoPlayer;
