/**
 * Video utility functions for handling various video sources and formats
 */

/**
 * Detects the type of video source from a URL
 * @param {string} url - The video URL
 * @returns {string} - The detected video type ('youtube', 'linkedin', 'azure', 'direct', 'unknown')
 */
export const detectVideoType = (url) => {
  if (!url || typeof url !== 'string') return 'unknown';
  
  const urlLower = url.toLowerCase();
  
  // YouTube detection
  if (urlLower.includes('youtube.com') || urlLower.includes('youtu.be')) {
    return 'youtube';
  }
  
  // LinkedIn detection
  if (urlLower.includes('linkedin.com')) {
    return 'linkedin';
  }
  
  // Azure Media Services detection
  if (urlLower.includes('.streaming.media.azure.net') || 
      urlLower.includes('azureedge.net') ||
      urlLower.includes('.blob.core.windows.net')) {
    return 'azure';
  }
  
  // Direct video file detection
  if (urlLower.match(/\.(mp4|webm|ogg|avi|mov|wmv|flv|mkv)(\?|$)/)) {
    return 'direct';
  }
  
  return 'direct'; // Default to direct for unknown types
};

/**
 * Extracts YouTube video ID from various YouTube URL formats
 * @param {string} url - YouTube URL
 * @returns {string|null} - Video ID or null if not found
 */
export const extractYouTubeVideoId = (url) => {
  if (!url) return null;
  
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
    /youtube\.com\/v\/([^&\n?#]+)/,
    /youtube\.com\/user\/[^\/]+#p\/[a-z]\/[0-9]+\/([^&\n?#]+)/
  ];
  
  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }
  
  return null;
};

/**
 * Converts YouTube URL to embed format
 * @param {string} url - YouTube URL
 * @param {object} options - Embed options (autoplay, mute, etc.)
 * @returns {string} - Embed URL
 */
export const getYouTubeEmbedUrl = (url, options = {}) => {
  const { autoplay = false, mute = false, controls = true, start = null, end = null } = options;
  
  const videoId = extractYouTubeVideoId(url);
  if (!videoId) return url;
  
  const params = new URLSearchParams();
  if (autoplay) params.set('autoplay', '1');
  if (mute) params.set('mute', '1');
  if (!controls) params.set('controls', '0');
  if (start) params.set('start', start.toString());
  if (end) params.set('end', end.toString());
  
  const queryString = params.toString();
  return `https://www.youtube.com/embed/${videoId}${queryString ? '?' + queryString : ''}`;
};

/**
 * Validates if a URL is a valid video source
 * @param {string} url - URL to validate
 * @returns {boolean} - True if valid video URL
 */
export const isValidVideoUrl = (url) => {
  if (!url || typeof url !== 'string') return false;
  
  try {
    const urlObj = new URL(url);
    const validProtocols = ['http:', 'https:'];
    return validProtocols.includes(urlObj.protocol);
  } catch {
    return false;
  }
};

/**
 * Gets the appropriate MIME type for a video file based on its extension
 * @param {string} url - Video file URL
 * @returns {string} - MIME type
 */
export const getVideoMimeType = (url) => {
  if (!url) return 'video/mp4';
  
  const extension = url.split('.').pop()?.toLowerCase().split('?')[0];
  
  const mimeTypes = {
    'mp4': 'video/mp4',
    'webm': 'video/webm',
    'ogg': 'video/ogg',
    'ogv': 'video/ogg',
    'avi': 'video/x-msvideo',
    'mov': 'video/quicktime',
    'wmv': 'video/x-ms-wmv',
    'flv': 'video/x-flv',
    'mkv': 'video/x-matroska',
    'm4v': 'video/mp4',
    '3gp': 'video/3gpp',
    '3g2': 'video/3gpp2'
  };
  
  return mimeTypes[extension] || 'video/mp4';
};

/**
 * Generates alternative video sources with different formats
 * @param {string} baseUrl - Base video URL
 * @returns {Array} - Array of alternative source objects
 */
export const generateAlternativeSources = (baseUrl) => {
  if (!baseUrl) return [];
  
  const baseName = baseUrl.replace(/\.[^.]+$/, '');
  const alternatives = [];
  
  // Generate WebM alternative
  alternatives.push({
    src: `${baseName}.webm`,
    type: 'video/webm'
  });
  
  // Generate OGG alternative
  alternatives.push({
    src: `${baseName}.ogg`,
    type: 'video/ogg'
  });
  
  return alternatives;
};

/**
 * Constructs Azure Media Services URL with SAS token
 * @param {string} baseUrl - Base Azure URL
 * @param {string} sasToken - SAS token
 * @returns {string} - URL with SAS token
 */
export const addAzureSasToken = (baseUrl, sasToken) => {
  if (!baseUrl || !sasToken) return baseUrl;
  
  const separator = baseUrl.includes('?') ? '&' : '?';
  return `${baseUrl}${separator}${sasToken}`;
};

/**
 * Checks if the browser supports autoplay
 * @returns {Promise<boolean>} - Promise resolving to autoplay support
 */
export const checkAutoplaySupport = async () => {
  try {
    const video = document.createElement('video');
    video.muted = true;
    video.src = 'data:video/mp4;base64,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';
    
    const playPromise = video.play();
    if (playPromise !== undefined) {
      await playPromise;
      return true;
    }
    return false;
  } catch {
    return false;
  }
};

/**
 * Formats video duration from seconds to human readable format
 * @param {number} seconds - Duration in seconds
 * @returns {string} - Formatted duration (e.g., "1:23:45" or "5:30")
 */
export const formatVideoDuration = (seconds) => {
  if (!seconds || isNaN(seconds)) return '0:00';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
};

/**
 * Generates a thumbnail URL for a video (if supported by the platform)
 * @param {string} videoUrl - Video URL
 * @param {string} quality - Thumbnail quality ('default', 'medium', 'high', 'maxres')
 * @returns {string|null} - Thumbnail URL or null if not supported
 */
export const getVideoThumbnail = (videoUrl, quality = 'medium') => {
  const videoType = detectVideoType(videoUrl);
  
  if (videoType === 'youtube') {
    const videoId = extractYouTubeVideoId(videoUrl);
    if (videoId) {
      const qualityMap = {
        'default': 'default',
        'medium': 'mqdefault',
        'high': 'hqdefault',
        'maxres': 'maxresdefault'
      };
      
      const thumbnailQuality = qualityMap[quality] || 'mqdefault';
      return `https://img.youtube.com/vi/${videoId}/${thumbnailQuality}.jpg`;
    }
  }
  
  // Add support for other platforms as needed
  return null;
};

/**
 * Checks if a video URL requires CORS handling
 * @param {string} url - Video URL
 * @returns {boolean} - True if CORS handling is needed
 */
export const requiresCorsHandling = (url) => {
  if (!url) return false;
  
  try {
    const urlObj = new URL(url);
    const currentOrigin = window.location.origin;
    return urlObj.origin !== currentOrigin;
  } catch {
    return false;
  }
};

/**
 * Default video player configuration
 */
export const defaultVideoConfig = {
  autoplay: true,
  muted: true,
  controls: true,
  preload: 'metadata',
  playsinline: true,
  retryAttempts: 3,
  retryDelay: 1000,
  loadingTimeout: 30000
};

/**
 * Browser-specific video format support detection
 * @returns {object} - Object with format support information
 */
export const getVideoFormatSupport = () => {
  const video = document.createElement('video');
  
  return {
    mp4: video.canPlayType('video/mp4') !== '',
    webm: video.canPlayType('video/webm') !== '',
    ogg: video.canPlayType('video/ogg') !== '',
    hls: video.canPlayType('application/vnd.apple.mpegurl') !== '',
    dash: video.canPlayType('application/dash+xml') !== ''
  };
};
